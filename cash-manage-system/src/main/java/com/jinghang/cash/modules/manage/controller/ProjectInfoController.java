package com.jinghang.cash.modules.manage.controller;

import com.jinghang.cash.api.ProjectInfoApi;
import com.jinghang.cash.api.vo.ProjectInfoVO;
import com.jinghang.cash.service.ProjectInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目信息控制器
 * 实现ProjectInfoApi接口，提供项目信息查询服务
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@RestController
@RequestMapping("/api")
public class ProjectInfoController implements ProjectInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoController.class);

    @Autowired
    private ProjectInfoService projectInfoService;

    /**
     * 根据项目编码查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息
     */
    @Override
    public ProjectInfoVO queryProjectInfo(@PathVariable("projectCode") String projectCode) {
        logger.info("接收到查询项目信息请求，projectCode: {}", projectCode);

        try {
            // 参数校验
            if (projectCode == null || projectCode.trim().isEmpty()) {
                logger.warn("项目编码为空");
                return null;
            }

            // 调用业务服务
            ProjectInfoVO projectInfo = projectInfoService.queryProjectInfo(projectCode.trim());

            if (projectInfo == null) {
                logger.info("未找到项目信息，projectCode: {}", projectCode);
                return null;
            }

            logger.info("查询项目信息成功，projectCode: {}", projectCode);
            return projectInfo;

        } catch (Exception e) {
            logger.error("查询项目信息异常，projectCode: {}", projectCode, e);
            return null;
        }
    }
}
