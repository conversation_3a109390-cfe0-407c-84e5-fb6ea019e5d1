package com.jinghang.cash.pojo.project;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 项目要素扩展实体
 * 基于Flow系统的ProjectElementsExt设计
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@TableName(value = "project_elements_ext")
@Data
public class ProjectElementsExt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 关联的项目唯一编码
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 年利率基数(天) (如 360或365)
     */
    @TableField(value = "interest_days_basis")
    private String interestDaysBasis;

    /**
     * 是否支持线下跨日还款
     */
    @TableField(value = "allow_cross_day_repay")
    private String allowCrossDayRepay;

    /**
     * 风控模型渠道
     */
    @TableField(value = "risk_model_channel")
    private String riskModelChannel;

    /**
     * 放款支付渠道
     */
    @TableField(value = "loan_payment_channel")
    private String loanPaymentChannel;

    /**
     * 扣款绑卡渠道
     */
    @TableField(value = "deduction_bind_card_channel")
    private String deductionBindCardChannel;

    /**
     * 扣款商户号
     */
    @TableField(value = "deduction_merchant_code")
    private String deductionMerchantCode;

    /**
     * 签章渠道
     */
    @TableField(value = "sign_channel")
    private String signChannel;

    /**
     * 逾期短信发送方
     */
    @TableField(value = "overdue_sms_sender")
    private String overdueSmsSender;

    /**
     * 短信渠道
     */
    @TableField(value = "sms_channel")
    private String smsChannel;

    /**
     * 逾期宽限期类型 (SQ:首期, MQ:每期)
     */
    @TableField(value = "grace_period_type")
    private String gracePeriodType;

    /**
     * 逾期宽限期(天)
     */
    @TableField(value = "grace_period_days")
    private String gracePeriodDays;

    /**
     * 节假日是否顺延
     */
    @TableField(value = "holiday_postpone")
    private String holidayPostpone;

    /**
     * 征信查询方
     */
    @TableField(value = "credit_query_party")
    private String creditQueryParty;

    /**
     * 征信上报方
     */
    @TableField(value = "credit_report_sender")
    private String creditReportSender;

    /**
     * 催收方
     */
    @TableField(value = "collection_party")
    private String collectionParty;

    /**
     * 是否推送催收数据
     */
    @TableField(value = "push_collection_data")
    private String pushCollectionData;

    /**
     * 是否支持催收减免
     */
    @TableField(value = "allow_collection_waiver")
    private String allowCollectionWaiver;

    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
}
