package com.jinghang.cash.pojo.project;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同模板配置表-资金方
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:55
 */
@Getter
@Setter
@TableName("project_contract_capital")
public class ProjectContractCapital implements Serializable {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目唯一编码
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    @Column(name = "template_code")
    private String templateCode;

    /**
     * 合同英文简称(文件名)
     */
    @Column(name = "contract_file_name")
    private String contractFileName;

    /**
     * 合同模板中文描述
     */
    @Column(name = "contract_description")
    private String contractDescription;

    /**
     * 合同签署阶段
     */
    @Column(name = "sign_stage")
    private String signStage;

    /**
     * 模板归属方
     */
    @Column(name = "template_owner")
    private String templateOwner;

    /**
     * 是否融担签章(0=否,1=是)
     */
    @Column(name = "is_rd_signature")
    private String isRdSignature;

    /**
     * 签章类型
     */
    @Column(name = "seal_type")
    private String sealType;

    /**
     * 是否回传资金方(0=否,1=是)
     */
    @Column(name = "is_return_to_capital")
    private String isReturnToCapital;

    /**
     * 模板备注
     */
    private String remark;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
